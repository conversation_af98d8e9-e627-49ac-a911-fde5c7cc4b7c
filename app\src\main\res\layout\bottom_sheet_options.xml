<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/bottom_sheet_background"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Add Content"
        android:textColor="#FFFFFF"
        android:textSize="18sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <!-- Image Option -->
    <LinearLayout
        android:id="@+id/option_image"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="?attr/selectableItemBackground"
        android:gravity="center_vertical">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_image"
            android:tint="#FFFFFF" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Image"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:layout_marginStart="16dp" />

    </LinearLayout>

    <!-- Document Option -->
    <LinearLayout
        android:id="@+id/option_document"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="?attr/selectableItemBackground"
        android:gravity="center_vertical">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_document"
            android:tint="#FFFFFF" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Document"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:layout_marginStart="16dp" />

    </LinearLayout>

    <!-- Camera Option -->
    <LinearLayout
        android:id="@+id/option_camera"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="?attr/selectableItemBackground"
        android:gravity="center_vertical">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_camera"
            android:tint="#FFFFFF" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Camera"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:layout_marginStart="16dp" />

    </LinearLayout>

</LinearLayout>
