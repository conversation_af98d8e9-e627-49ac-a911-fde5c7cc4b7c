package com.example.jubuddyai1

import android.app.DownloadManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.net.Uri
import android.net.http.SslError
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.Settings
import android.view.MenuItem
import android.view.View
import android.webkit.CookieManager
import android.webkit.DownloadListener
import android.webkit.SslErrorHandler
import android.webkit.URLUtil
import android.webkit.WebChromeClient
import android.webkit.WebResourceRequest
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.ProgressBar
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import android.os.StrictMode
import android.webkit.MimeTypeMap
import android.webkit.ValueCallback
import java.net.HttpURLConnection
import java.net.URL
import java.io.FileOutputStream
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import android.media.MediaScannerConnection

class WebViewActivity : AppCompatActivity() {
    
    private lateinit var webView: WebView
    private lateinit var progressBar: ProgressBar
    private var downloadID: Long = -1
    private val STORAGE_PERMISSION_CODE = 1001
    private val MANAGE_STORAGE_PERMISSION_CODE = 2001
    private var pendingDownloadInfo: DownloadInfo? = null
    private val PICK_FILE_REQUEST_CODE = 3001
    
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        var allGranted = true
        permissions.entries.forEach {
            if (!it.value) {
                allGranted = false
                return@forEach
            }
        }
        
        if (allGranted) {
            pendingDownloadInfo?.let {
                performDownload(it.url, it.userAgent, it.contentDisposition, it.mimeType, it.contentLength)
                pendingDownloadInfo = null
            }
        } else {
            Toast.makeText(this, "Storage permissions required for downloads", Toast.LENGTH_LONG).show()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        // Fix StrictMode for network operations
        val policy = StrictMode.ThreadPolicy.Builder().permitAll().build()
        StrictMode.setThreadPolicy(policy)
        
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_web_view)

        // Initialize views
        webView = findViewById(R.id.webView)
        progressBar = findViewById(R.id.progressBar)
        
        // Set up ActionBar
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowHomeEnabled(true)
            title = "Back to Chat"
        }
        
        // Check and request storage permissions for downloads
        checkPermissionsForDownload()
        
        // Configure WebView
        webView.settings.apply {
            javaScriptEnabled = true
            domStorageEnabled = true
            loadsImagesAutomatically = true
            setSupportZoom(true)
            builtInZoomControls = true
            displayZoomControls = false
            allowFileAccess = true
            allowContentAccess = true
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                safeBrowsingEnabled = true
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
                CookieManager.getInstance().setAcceptThirdPartyCookies(webView, true)
            }
            databaseEnabled = true
            useWideViewPort = true
            loadWithOverviewMode = true
            cacheMode = WebSettings.LOAD_NO_CACHE
        }
        
        // Set up WebView Client to handle all URL navigation
        webView.webViewClient = object : WebViewClient() {
            override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
                // For Android 6 and below
                // Check if this is a download link
                if (isDownloadableFile(url)) {
                    startDownload(url, "", "")
                    return true
                }
                
                // Special handling for external apps
                if (url.startsWith("tel:") || url.startsWith("mailto:") || 
                    url.startsWith("whatsapp:") || url.startsWith("market:")) {
                    try {
                        val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                        startActivity(intent)
                        return true
                    } catch (e: Exception) {
                        Toast.makeText(this@WebViewActivity, "No app found to handle this link", Toast.LENGTH_SHORT).show()
                    }
                }
                
                // Keep all other URL navigation within the WebView
                view.loadUrl(url)
                return true
            }
            
            // For Android 7+
            override fun shouldOverrideUrlLoading(view: WebView, request: WebResourceRequest): Boolean {
                val url = request.url.toString()
                
                // Check if this is a download link
                if (isDownloadableFile(url)) {
                    startDownload(url, "", "")
                    return true
                }
                
                // Special handling for external apps
                if (url.startsWith("tel:") || url.startsWith("mailto:") || 
                    url.startsWith("whatsapp:") || url.startsWith("market:")) {
                    try {
                        val intent = Intent(Intent.ACTION_VIEW, request.url)
                        startActivity(intent)
                        return true
                } catch (e: Exception) {
                        Toast.makeText(this@WebViewActivity, "No app found to handle this link", Toast.LENGTH_SHORT).show()
                    }
                }
                
                // Keep all other URL navigation within the WebView
                view.loadUrl(url)
                return true
            }

            override fun onReceivedSslError(view: WebView, handler: SslErrorHandler, error: SslError) {
                handler.proceed() // Proceed with SSL error - needed for some sites
            }

            override fun onPageFinished(view: WebView, url: String) {
                progressBar.visibility = View.GONE
                supportActionBar?.title = view.title ?: "Web Browser"
                
                // Enhanced script to detect and capture download links
                val javascript = """
                javascript:(function() {
                    console.log('Download interceptor initialized');
                    
                    // Function to intercept downloads
                    function interceptDownloadLink(event, url) {
                        event.preventDefault();
                        event.stopPropagation();
                        console.log('Intercepted download: ' + url);
                        window.Android.downloadFile(url);
                        return false;
                    }
                    
                    // Helper to detect download links
                    function isLikelyDownloadLink(elem) {
                        if (!elem || !elem.href) return false;
                        
                        var url = elem.href.toLowerCase();
                        var text = (elem.textContent || '').toLowerCase();
                        var classes = (elem.className || '').toLowerCase();
                        
                        // Check URL patterns
                        if (url.endsWith('.pdf') || url.endsWith('.doc') || url.endsWith('.docx') || 
                            url.endsWith('.xls') || url.endsWith('.xlsx') || url.endsWith('.zip') || 
                            url.endsWith('.rar') || url.endsWith('.mp3') || url.endsWith('.mp4') ||
                            url.endsWith('.txt') || url.endsWith('.csv') || url.endsWith('.ppt') ||
                            url.endsWith('.pptx')) {
                            return true;
                        }
                        
                        // Check URL keywords
                        if (url.includes('/download') || url.includes('dl=1') || 
                            url.includes('download=true') || url.includes('attachment') ||
                            url.includes('export=download') || url.includes('filedownload')) {
                            return true;
                        }
                        
                        // Check element attributes
                        if (elem.hasAttribute('download') || 
                            elem.getAttribute('type') === 'application/octet-stream' || 
                            elem.getAttribute('data-downloadurl')) {
                            return true;
                        }
                        
                        // Check text content
                        if (text.includes('download') || text.includes('save') || 
                            text.includes('export') || text.includes('get file')) {
                            return true;
                        }
                        
                        // Check class names 
                        if (classes.includes('download') || classes.includes('btn-download') || 
                            classes.includes('download-link') || classes.includes('download-button')) {
                            return true;
                        }
                        
                        return false;
                    }
                    
                    // Handle all link clicks
                    document.addEventListener('click', function(e) {
                        var target = e.target;
                        
                        // Navigate up to find if a parent is a link
                        while (target && target.tagName !== 'A') {
                            target = target.parentElement;
                        }
                        
                        // If we found a link and it's a download link, intercept it
                        if (target && target.tagName === 'A' && isLikelyDownloadLink(target)) {
                            console.log('Download link clicked: ' + target.href);
                            return interceptDownloadLink(e, target.href);
                        }
                    }, true);
                    
                    // Find and modify download links
                    try {
                        // Find all links
                        var links = document.getElementsByTagName('a');
                        for (var i = 0; i < links.length; i++) {
                            var link = links[i];
                            
                            // If it looks like a download link, modify its click behavior
                            if (isLikelyDownloadLink(link)) {
                                console.log('Found download link: ' + link.href);
                                link.addEventListener('click', function(e) {
                                    return interceptDownloadLink(e, this.href);
                                }, true);
                            }
                        }
                        
                        // Also look for buttons that might trigger downloads
                        var buttons = document.querySelectorAll('button, .button, [role="button"]');
                        for (var j = 0; j < buttons.length; j++) {
                            var button = buttons[j];
                            var buttonText = (button.textContent || '').toLowerCase();
                            
                            if (buttonText.includes('download') || buttonText.includes('save') || 
                                buttonText.includes('export') || buttonText.includes('get file')) {
                                console.log('Found download button: ' + button.textContent);
                                button.addEventListener('click', function(e) {
                                    window.Android.logMessage('Download button clicked: ' + this.textContent);
                                }, true);
                            }
                        }
                    } catch (err) {
                        console.error('Error in download interceptor: ' + err.message);
                    }
                })();
                """
                
                webView.evaluateJavascript(javascript, null)
            }
        }
        
        // Add JavaScript interface
        webView.addJavascriptInterface(JavaScriptInterface(this), "Android")
        
        // Set up production-ready download listener
        webView.setDownloadListener { url, userAgent, contentDisposition, mimetype, contentLength ->
            logDownloadRequest(url, mimetype, contentLength)

            // Enhanced download handling with proper MIME type detection
            val enhancedMimeType = enhanceMimeTypeDetection(url, contentDisposition, mimetype)
            val properFileName = generateProperFileName(url, contentDisposition, enhancedMimeType)

            // Request permissions if needed
            if (!checkAllPermissions()) {
                pendingDownloadInfo = DownloadInfo(url, userAgent, contentDisposition, enhancedMimeType, contentLength)
                requestAllPermissions()
            } else {
                performEnhancedDownload(url, userAgent, contentDisposition, enhancedMimeType, contentLength, properFileName)
            }
        }
        
        // Set up WebChromeClient for progress updates
        webView.webChromeClient = object : WebChromeClient() {
            override fun onProgressChanged(view: WebView, newProgress: Int) {
                progressBar.progress = newProgress
                if (newProgress < 100) {
                    progressBar.visibility = View.VISIBLE
                } else {
                    progressBar.visibility = View.GONE
                }
            }
            
            override fun onReceivedTitle(view: WebView, title: String) {
                supportActionBar?.title = title
            }
        }
        
        // Register broadcast receiver to handle download completion
        registerReceiver(onDownloadComplete, IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE))
        
        // Load the URL from the intent or default to Google
        val url = intent.getStringExtra("url") ?: "https://www.google.com"
        webView.loadUrl(url)
        
        // Handle back button press with the new callback method
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                if (webView.canGoBack()) {
                    webView.goBack()
                } else {
                    finish()
                }
            }
        })
    }
    
    private fun isDownloadableFile(url: String): Boolean {
        val lowercaseUrl = url.lowercase()
        return lowercaseUrl.endsWith(".pdf") || 
               lowercaseUrl.endsWith(".doc") || 
               lowercaseUrl.endsWith(".docx") ||
               lowercaseUrl.endsWith(".xls") || 
               lowercaseUrl.endsWith(".xlsx") || 
               lowercaseUrl.endsWith(".ppt") ||
               lowercaseUrl.endsWith(".pptx") ||
               lowercaseUrl.endsWith(".zip") || 
               lowercaseUrl.endsWith(".rar") ||
               lowercaseUrl.endsWith(".apk") ||
               lowercaseUrl.endsWith(".mp3") ||
               lowercaseUrl.endsWith(".mp4") ||
               lowercaseUrl.endsWith(".txt") ||
               lowercaseUrl.endsWith(".csv") ||
               lowercaseUrl.endsWith(".json") ||
               lowercaseUrl.contains("/download") || 
               lowercaseUrl.contains("attachment") ||
               lowercaseUrl.contains("filedownload") ||
               lowercaseUrl.contains("dl=1") ||
               lowercaseUrl.contains("download=true") ||
               lowercaseUrl.contains("export=download")
    }
    
    // Class to store download information while waiting for permission
    private data class DownloadInfo(
        val url: String,
        val userAgent: String,
        val contentDisposition: String,
        val mimeType: String,
        val contentLength: Long
    )
    
    private fun logDownloadRequest(url: String, mimeType: String, contentLength: Long) {
        println("Download requested: $url")
        println("MIME Type: $mimeType")
        println("Content Length: $contentLength")
    }
    
    private fun checkAllPermissions(): Boolean {
        // For Android 10 (Q) and below
        if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.Q) {
            return ContextCompat.checkSelfPermission(
                this, 
                android.Manifest.permission.WRITE_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED
        }
        
        // For Android 11 (R) and above, we don't need WRITE_EXTERNAL_STORAGE for Downloads folder
        return true
    }
    
    private fun requestAllPermissions() {
        if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.Q) {
            // Request WRITE_EXTERNAL_STORAGE for Android 10 and below
            requestPermissionLauncher.launch(
                arrayOf(android.Manifest.permission.WRITE_EXTERNAL_STORAGE)
            )
        } else if (Build.VERSION.SDK_INT == Build.VERSION_CODES.R) {
            // For Android 11, we can request MANAGE_EXTERNAL_STORAGE but only for special apps
            // Most apps should use scoped storage instead
            try {
                val intent = Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION)
                intent.data = Uri.parse("package:$packageName")
                startActivityForResult(intent, MANAGE_STORAGE_PERMISSION_CODE)
            } catch (e: Exception) {
                val intent = Intent(Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION)
                startActivityForResult(intent, MANAGE_STORAGE_PERMISSION_CODE)
            }
        }
        // For Android 12+ we don't need to request permission for Downloads folder
    }
    
    private fun checkPermissionsForDownload() {
        if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.Q) {
            if (ContextCompat.checkSelfPermission(
                this,
                android.Manifest.permission.WRITE_EXTERNAL_STORAGE
            ) != PackageManager.PERMISSION_GRANTED) {
                ActivityCompat.requestPermissions(
                    this,
                    arrayOf(android.Manifest.permission.WRITE_EXTERNAL_STORAGE),
                    STORAGE_PERMISSION_CODE
                )
            }
        }
    }
    
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == STORAGE_PERMISSION_CODE) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                // Permission granted, proceed with pending download if any
                pendingDownloadInfo?.let {
                    performDownload(it.url, it.userAgent, it.contentDisposition, it.mimeType, it.contentLength)
                    pendingDownloadInfo = null
                }
            } else {
                Toast.makeText(this, "Storage permission required to download files", Toast.LENGTH_LONG).show()
                // Show dialog explaining why permission is needed
                showPermissionExplanationDialog()
            }
        }
    }
    
    private fun showPermissionExplanationDialog() {
        AlertDialog.Builder(this)
            .setTitle("Storage Permission Required")
            .setMessage("To download files, we need permission to save them to your device. Please grant storage permission.")
            .setPositiveButton("Grant Permission") { _, _ ->
                requestAllPermissions()
            }
            .setNegativeButton("Cancel") { dialog, _ ->
                dialog.dismiss()
            }
            .create()
            .show()
    }
    
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == MANAGE_STORAGE_PERMISSION_CODE) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                if (Environment.isExternalStorageManager()) {
                    pendingDownloadInfo?.let {
                        performDownload(it.url, it.userAgent, it.contentDisposition, it.mimeType, it.contentLength)
                        pendingDownloadInfo = null
                    }
                } else {
                    Toast.makeText(this, "Storage permission is required to download files", Toast.LENGTH_LONG).show()
                }
            }
        }
    }
    
    private fun startDownload(url: String, contentDisposition: String, mimeType: String) {
        val enhancedMimeType = enhanceMimeTypeDetection(url, contentDisposition, mimeType)
        val properFileName = generateProperFileName(url, contentDisposition, enhancedMimeType)
        performEnhancedDownload(url, webView.settings.userAgentString, contentDisposition, enhancedMimeType, 0, properFileName)
    }

    /**
     * Enhanced MIME type detection using multiple sources
     */
    private fun enhanceMimeTypeDetection(url: String, contentDisposition: String?, originalMimeType: String?): String {
        // Priority order: Content-Disposition header > URL extension > original MIME type > default

        // 1. Try to extract MIME type from Content-Disposition header
        contentDisposition?.let { disposition ->
            val mimeFromDisposition = extractMimeTypeFromDisposition(disposition)
            if (mimeFromDisposition != null) return mimeFromDisposition
        }

        // 2. Try to determine MIME type from URL extension
        val mimeFromUrl = getMimeTypeFromUrl(url)
        if (mimeFromUrl != null) return mimeFromUrl

        // 3. Use original MIME type if available and not generic
        if (!originalMimeType.isNullOrEmpty() &&
            originalMimeType != "application/octet-stream" &&
            originalMimeType != "application/force-download") {
            return originalMimeType
        }

        // 4. Default fallback
        return "application/octet-stream"
    }

    /**
     * Extract MIME type from Content-Disposition header
     */
    private fun extractMimeTypeFromDisposition(contentDisposition: String): String? {
        // Look for filename with extension in Content-Disposition
        val filenameRegex = """filename[*]?=(?:"([^"]+)"|([^;]+))""".toRegex(RegexOption.IGNORE_CASE)
        val match = filenameRegex.find(contentDisposition)
        val filename = match?.groupValues?.get(1) ?: match?.groupValues?.get(2)

        return filename?.let { getMimeTypeFromFileName(it) }
    }

    /**
     * Get MIME type from URL extension
     */
    private fun getMimeTypeFromUrl(url: String): String? {
        return try {
            val uri = Uri.parse(url)
            val path = uri.path ?: return null
            val extension = MimeTypeMap.getFileExtensionFromUrl(path)
            if (extension.isNotEmpty()) {
                MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension.lowercase())
            } else null
        } catch (e: Exception) {
            null
        }
    }

    /**
     * Get MIME type from filename
     */
    private fun getMimeTypeFromFileName(filename: String): String? {
        val extension = filename.substringAfterLast('.', "")
        return if (extension.isNotEmpty()) {
            MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension.lowercase())
        } else null
    }

    /**
     * Generate proper filename with correct extension
     */
    private fun generateProperFileName(url: String, contentDisposition: String?, mimeType: String): String {
        // Try URLUtil.guessFileName first (it's quite good)
        var filename = URLUtil.guessFileName(url, contentDisposition, mimeType)

        // If filename has .bin extension, try to fix it
        if (filename.endsWith(".bin")) {
            val properExtension = getExtensionFromMimeType(mimeType)
            if (properExtension != null) {
                filename = filename.removeSuffix(".bin") + ".$properExtension"
            }
        }

        // Ensure filename is safe
        filename = sanitizeFileName(filename)

        return filename
    }

    /**
     * Get file extension from MIME type
     */
    private fun getExtensionFromMimeType(mimeType: String): String? {
        return when (mimeType.lowercase()) {
            "application/pdf" -> "pdf"
            "image/jpeg" -> "jpg"
            "image/png" -> "png"
            "image/gif" -> "gif"
            "image/webp" -> "webp"
            "text/plain" -> "txt"
            "text/html" -> "html"
            "application/zip" -> "zip"
            "application/x-rar-compressed" -> "rar"
            "application/vnd.ms-excel" -> "xls"
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" -> "xlsx"
            "application/msword" -> "doc"
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document" -> "docx"
            "application/vnd.ms-powerpoint" -> "ppt"
            "application/vnd.openxmlformats-officedocument.presentationml.presentation" -> "pptx"
            "audio/mpeg" -> "mp3"
            "audio/wav" -> "wav"
            "video/mp4" -> "mp4"
            "video/avi" -> "avi"
            else -> MimeTypeMap.getSingleton().getExtensionFromMimeType(mimeType)
        }
    }

    /**
     * Sanitize filename to remove invalid characters
     */
    private fun sanitizeFileName(filename: String): String {
        return filename.replace(Regex("[\\\\/:*?\"<>|]"), "_")
            .replace(Regex("\\s+"), "_")
            .take(255) // Limit filename length
    }
    
    /**
     * Enhanced download method with proper MIME type handling and progress notifications
     */
    private fun performEnhancedDownload(url: String, userAgent: String, contentDisposition: String, mimeType: String, contentLength: Long, fileName: String) {
        try {
            // Use DownloadManager for better user experience
            val request = DownloadManager.Request(Uri.parse(url)).apply {
                setTitle("Downloading $fileName")
                setDescription("Download in progress...")
                setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED)
                setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, fileName)
                setAllowedOverMetered(true)
                setAllowedOverRoaming(true)

                // Set proper headers
                addRequestHeader("User-Agent", userAgent)
                if (contentDisposition.isNotEmpty()) {
                    addRequestHeader("Content-Disposition", contentDisposition)
                }

                // Set MIME type
                setMimeType(mimeType)
            }

            val downloadManager = getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
            downloadID = downloadManager.enqueue(request)

            // Show user feedback
            Toast.makeText(this, "Download started: $fileName", Toast.LENGTH_LONG).show()

        } catch (e: Exception) {
            e.printStackTrace()
            Toast.makeText(this, "Download failed: ${e.message}", Toast.LENGTH_LONG).show()

            // Fallback to direct download
            directDownloadWithProgress(url, fileName, userAgent)
        }
    }

    private fun performDownload(url: String, userAgent: String, contentDisposition: String, mimeType: String, contentLength: Long) {
        try {
            // Print debug info
            println("Download URL: $url")
            println("User Agent: $userAgent")
            println("Content Disposition: $contentDisposition")
            println("MIME Type: $mimeType")
            
            // Create download directory if it doesn't exist
            val downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
            if (!downloadsDir.exists()) {
                downloadsDir.mkdirs()
            }
            
            // Get filename from URL
            var filename = URLUtil.guessFileName(url, contentDisposition, mimeType)
            
            // Ensure unique filename
            val file = File(downloadsDir, filename)
            if (file.exists()) {
                val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
                val dotIndex = filename.lastIndexOf(".")
                filename = if (dotIndex != -1) {
                    val name = filename.substring(0, dotIndex)
                    val extension = filename.substring(dotIndex)
                    "${name}_$timestamp$extension"
                } else {
                    "${filename}_$timestamp"
                }
            }
            
            // The most reliable method for most devices is to use direct download
            // instead of using DownloadManager which can fail for various reasons
            directDownloadWithProgress(url, filename, userAgent)
            
        } catch (e: Exception) {
            e.printStackTrace()
            Toast.makeText(this, "Could not start download: ${e.message}", Toast.LENGTH_LONG).show()
            // Try fallback method
            tryFallbackDownload(url)
        }
    }
    
    // Add this new method for reliable direct downloading with progress updates
    private fun directDownloadWithProgress(url: String, filename: String, userAgent: String) {
        Toast.makeText(this, "Starting download: $filename", Toast.LENGTH_SHORT).show()
        
        // Use coroutine for background processing
        GlobalScope.launch(Dispatchers.IO) {
            var connection: HttpURLConnection? = null
            var input: java.io.InputStream? = null
            var output: FileOutputStream? = null
            var bytesRead = 0
            
            try {
                // Create download directory
                val downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
                if (!downloadsDir.exists()) {
                    downloadsDir.mkdirs()
                }
                
                // Create the destination file
                val destinationFile = File(downloadsDir, filename)
                
                // Add redundant check to ensure directory exists
                destinationFile.parentFile?.mkdirs()
                
                // Create connection with retry mechanism
                val urlObj = URL(url)
                var retryCount = 0
                val maxRetries = 3
                
                while (retryCount < maxRetries) {
                    try {
                        connection = urlObj.openConnection() as HttpURLConnection
                        connection.instanceFollowRedirects = true
                        connection.requestMethod = "GET"
                        connection.connectTimeout = 30000
                        connection.readTimeout = 30000
                        
                        // Critical: Add headers to simulate a browser download
                        connection.setRequestProperty("User-Agent", userAgent ?: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36")
                        connection.setRequestProperty("Accept", "*/*")
                        connection.setRequestProperty("Accept-Encoding", "identity") // Force uncompressed
                        connection.setRequestProperty("Connection", "keep-alive")
                        
                        // Get cookies from WebView
                        val cookies = CookieManager.getInstance().getCookie(url)
                        if (!cookies.isNullOrEmpty()) {
                            connection.setRequestProperty("Cookie", cookies)
                        }
                        
                        // Add referer for sites that check this
                        connection.setRequestProperty("Referer", url)
                        
                        // Connect with timeout
                        connection.connect()
                        break // Success, exit retry loop
                    } catch (e: Exception) {
                        retryCount++
                        if (retryCount >= maxRetries) {
                            throw e // Rethrow if all retries failed
                        }
                        
                        // Wait before retry with exponential backoff
                        Thread.sleep((1000 * retryCount).toLong())
                        connection?.disconnect()
                    }
                }
                
                // Check response code - handle redirects explicitly
                var redirectUrl = url
                var redirectConnection = connection
                var responseCode = redirectConnection?.responseCode ?: -1
                
                // Handle redirects (up to 5 levels)
                var redirectCount = 0
                while ((responseCode == HttpURLConnection.HTTP_MOVED_TEMP || 
                        responseCode == HttpURLConnection.HTTP_MOVED_PERM || 
                        responseCode == HttpURLConnection.HTTP_SEE_OTHER) && 
                        redirectCount < 5 && redirectConnection != null) {
                    
                    redirectCount++
                    
                    // Get the redirect url from location header
                    redirectUrl = redirectConnection.getHeaderField("Location") ?: url
                    
                    // Close the previous connection
                    redirectConnection.disconnect()
                    
                    // Open a new connection to the redirect URL
                    val redirectUrlObj = URL(redirectUrl)
                    redirectConnection = redirectUrlObj.openConnection() as? HttpURLConnection
                    
                    // Check if the new connection is valid
                    if (redirectConnection == null) {
                        withContext(Dispatchers.Main) {
                            Toast.makeText(this@WebViewActivity,
                                "Failed to connect to redirect URL. Trying alternative method...",
                                Toast.LENGTH_LONG).show()
                            tryFallbackDownload(url)
                        }
                        return@launch
                    }
                    
                    redirectConnection.instanceFollowRedirects = true
                    redirectConnection.requestMethod = "GET"
                    redirectConnection.connectTimeout = 30000
                    redirectConnection.readTimeout = 30000
                    
                    // Set the same headers
                    redirectConnection.setRequestProperty("User-Agent", userAgent ?: "Mozilla/5.0")
                    redirectConnection.setRequestProperty("Accept", "*/*")
                    redirectConnection.setRequestProperty("Accept-Encoding", "identity")
                    redirectConnection.setRequestProperty("Connection", "keep-alive")
                    redirectConnection.setRequestProperty("Referer", url)
                    
                    val cookies = CookieManager.getInstance().getCookie(url)
                    if (!cookies.isNullOrEmpty()) {
                        redirectConnection.setRequestProperty("Cookie", cookies)
                    }
                    
                    redirectConnection.connect()
                    responseCode = redirectConnection.responseCode
                }
                
                connection = redirectConnection
                
                // Final check for success
                if (connection == null || responseCode != HttpURLConnection.HTTP_OK) {
                    withContext(Dispatchers.Main) {
                        Toast.makeText(this@WebViewActivity,
                            "Server returned: $responseCode - ${connection?.responseMessage ?: "Unknown error"}. Trying alternative method...",
                            Toast.LENGTH_LONG).show()
                        
                        tryFallbackDownload(url)
                    }
                    return@launch
                }
                
                // Get content type for later file opening
                val contentType = connection.contentType ?: getMimeType(filename)
                
                // Open input stream from connection
                input = connection.inputStream
                
                // Create output stream to file
                output = FileOutputStream(destinationFile)
                
                // Get file size for progress reporting
                val fileSize = connection.contentLength.toLong().let { if (it < 0) Long.MAX_VALUE else it }
                
                // Download the file
                val buffer = ByteArray(8192)
                var totalBytesRead: Long = 0
                
                // Report start of download to main thread
                withContext(Dispatchers.Main) {
                    Toast.makeText(this@WebViewActivity, 
                        "Download started: $filename", 
                        Toast.LENGTH_SHORT).show()
                }
                
                // Process the download with progress updates
                while (true) {
                    val readResult = input?.read(buffer) ?: -1
                    if (readResult == -1) break
                    bytesRead = readResult
                    
                    output.write(buffer, 0, bytesRead)
                    totalBytesRead += bytesRead
                    
                    // Update progress every 10% but avoid division by zero
                    if (fileSize > 0 && fileSize < Long.MAX_VALUE && totalBytesRead % (fileSize / 10 + 1) < 8192) {
                        val progress = (totalBytesRead * 100 / fileSize).toInt()
                        withContext(Dispatchers.Main) {
                            // Only show progress at 10% intervals to avoid too many toasts
                            if (progress % 10 == 0) {
                                Toast.makeText(this@WebViewActivity, 
                                    "Downloading: $progress%", 
                                    Toast.LENGTH_SHORT).show()
                            }
                        }
                    }
                }
                
                // Flush and close streams
                output.flush()
                output.close()
                input?.close()
                
                // Add the file to MediaStore database so it's visible
                MediaScannerConnection.scanFile(
                    this@WebViewActivity,
                    arrayOf(destinationFile.absolutePath),
                    null
                ) { _, uri ->
                    // File is now visible in Downloads
                }
                
                // Report success and offer to open file
                withContext(Dispatchers.Main) {
                    Toast.makeText(this@WebViewActivity,
                        "Download complete: $filename",
                        Toast.LENGTH_LONG).show()
                    
                    // Make it visible in Downloads app
                    try {
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                            val downloadManager = getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
                            downloadManager.addCompletedDownload(
                                filename,
                                "Downloaded by JUBuddyAI",
                                true,
                                contentType,
                                destinationFile.absolutePath,
                                destinationFile.length(),
                                true
                            )
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                        // Even if this fails, the file should still be accessible
                    }
                    
                    // Show dialog to open the file
                    AlertDialog.Builder(this@WebViewActivity)
                        .setTitle("Download Complete")
                        .setMessage("File saved to Downloads. Do you want to open it now?")
                        .setPositiveButton("Open") { _, _ ->
                            openDownloadedFile(destinationFile, contentType)
                        }
                        .setNegativeButton("Later", null)
                        .show()
                }
                
            } catch (e: Exception) {
                e.printStackTrace()
                
                withContext(Dispatchers.Main) {
                    Toast.makeText(this@WebViewActivity,
                        "Direct download failed: ${e.message}. Trying fallback...",
                        Toast.LENGTH_LONG).show()
                    
                    // Try fallback method with better error handling
                    tryEnhancedFallbackDownload(url, filename)
                }
            } finally {
                try {
                    output?.close()
                    input?.close()
                    connection?.disconnect()
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }
    
    // Add this improved fallback method
    private fun tryEnhancedFallbackDownload(url: String, suggestedFilename: String? = null) {
        try {
            // Try to use the system download manager as a fallback
            val request = DownloadManager.Request(Uri.parse(url))
            val filename = suggestedFilename ?: URLUtil.guessFileName(url, null, null)
            
            // Critical settings for success
            request.setAllowedOverMetered(true)
            request.setAllowedOverRoaming(true)
            request.setAllowedNetworkTypes(DownloadManager.Request.NETWORK_WIFI or DownloadManager.Request.NETWORK_MOBILE)
            
            // Set download location
            request.setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, filename)
            
            // Set other properties
            request.setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED)
            request.setTitle(filename)
            request.setDescription("Downloading via fallback method")
            
            // Add request headers
            request.addRequestHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36")
            request.addRequestHeader("Accept", "*/*")
            request.addRequestHeader("Accept-Encoding", "identity")
            
            // Add cookies if available
            val cookies = CookieManager.getInstance().getCookie(url)
            if (!cookies.isNullOrEmpty()) {
                request.addRequestHeader("Cookie", cookies)
            }
            
            // Start download
            val dm = getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
            downloadID = dm.enqueue(request)
            
            // Register for completion to show open dialog
            val filter = IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE)
            registerReceiver(object : BroadcastReceiver() {
                override fun onReceive(context: Context, intent: Intent) {
                    val id = intent.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, -1)
                    if (id == downloadID) {
                        // Check if download was successful
                        val query = DownloadManager.Query().setFilterById(downloadID)
                        val cursor = dm.query(query)
                        
                        if (cursor.moveToFirst()) {
                            val statusIndex = cursor.getColumnIndex(DownloadManager.COLUMN_STATUS)
                            if (statusIndex >= 0 && cursor.getInt(statusIndex) == DownloadManager.STATUS_SUCCESSFUL) {
                                // Get the downloaded file's URI
                                val uriIndex = cursor.getColumnIndex(DownloadManager.COLUMN_LOCAL_URI)
                                if (uriIndex >= 0) {
                                    val uriString = cursor.getString(uriIndex)
                                    if (!uriString.isNullOrEmpty()) {
                                        try {
                                            val uri = Uri.parse(uriString)
                                            val filename = uri.lastPathSegment ?: "download"
                                            when (uri.scheme) {
                                                "file" -> {
                                                    val file = File(uri.path ?: "")
                                                    if (file.exists()) {
                                                        // Offer to open the file
                                                        AlertDialog.Builder(context)
                                                            .setTitle("Download Complete")
                                                            .setMessage("Do you want to open this file?")
                                                            .setPositiveButton("Open") { _, _ ->
                                                                openDownloadedFile(file, getMimeType(filename))
                                                            }
                                                            .setNegativeButton("Later", null)
                                                            .show()
                                                    }
                                                }
                                                "content" -> {
                                                    // For content URIs, use a different approach
                                                    val intent = Intent(Intent.ACTION_VIEW)
                                                    intent.setDataAndType(uri, getMimeType(uri.lastPathSegment ?: filename))
                                                    intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                                                    
                                                    if (intent.resolveActivity(packageManager) != null) {
                                                        AlertDialog.Builder(context)
                                                            .setTitle("Download Complete")
                                                            .setMessage("Do you want to open this file?")
                                                            .setPositiveButton("Open") { _, _ ->
                                                                startActivity(intent)
                                                            }
                                                            .setNegativeButton("Later", null)
                                                            .show()
                                                    }
                                                }
                                                else -> {
                                                    // Handle other URI schemes if needed
                                                    Toast.makeText(context, "Unknown file location", Toast.LENGTH_SHORT).show()
                                                }
                                            }
                                        } catch (e: Exception) {
                                            e.printStackTrace()
                                        }
                                    }
                                }
                            }
                        }
                        cursor.close()
                    }
                    
                    try {
                        unregisterReceiver(this)
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }, filter)
            
            Toast.makeText(this, "Trying enhanced fallback download...", Toast.LENGTH_SHORT).show()
            
        } catch (e: Exception) {
            e.printStackTrace()
            // If all else fails, try direct browser handling
            try {
                val intent = Intent(Intent.ACTION_VIEW).apply {
                    data = Uri.parse(url)
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                startActivity(intent)
            } catch (e2: Exception) {
                Toast.makeText(this, "All download methods failed, please try a different link.", Toast.LENGTH_LONG).show()
            }
        }
    }
    
    // JavaScript interface for debugging
    private class JavaScriptInterface(private val context: Context) {
        @android.webkit.JavascriptInterface
        fun logMessage(message: String) {
            println("JavaScriptInterface: $message")
        }
        
        @android.webkit.JavascriptInterface
        fun downloadFile(url: String) {
            if (url.isNotEmpty()) {
                (context as WebViewActivity).runOnUiThread {
                    Toast.makeText(context, "Intercepted download: $url", Toast.LENGTH_SHORT).show()
                    (context as WebViewActivity).startDownload(url, "", "")
                }
            }
        }
    }
    
    // Broadcast receiver to handle download completion
    private val onDownloadComplete = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val id = intent.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, -1)
            if (id == downloadID) {
                val downloadManager = getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
                val query = DownloadManager.Query().setFilterById(downloadID)
                val cursor = downloadManager.query(query)
                
                if (cursor.moveToFirst()) {
                    val statusColumnIndex = cursor.getColumnIndex(DownloadManager.COLUMN_STATUS)
                    val reasonColumnIndex = cursor.getColumnIndex(DownloadManager.COLUMN_REASON)
                    
                    if (statusColumnIndex >= 0) {
                        val status = cursor.getInt(statusColumnIndex)
                        
                        when (status) {
                            DownloadManager.STATUS_SUCCESSFUL -> {
                                Toast.makeText(context, "Download completed successfully", Toast.LENGTH_SHORT).show()
                                
                                // Get file info for opening
                                val localUriIndex = cursor.getColumnIndex(DownloadManager.COLUMN_LOCAL_URI)
                                val mimeTypeIndex = cursor.getColumnIndex(DownloadManager.COLUMN_MEDIA_TYPE)
                                
                                if (localUriIndex >= 0) {
                                    val localUriString = cursor.getString(localUriIndex)
                                    val mimeType = if (mimeTypeIndex >= 0) cursor.getString(mimeTypeIndex) else "*/*"
                                    
                                    if (!localUriString.isNullOrEmpty()) {
                                        try {
                                            val uri = Uri.parse(localUriString)
                                            val filename = uri.lastPathSegment ?: "download"
                                            when (uri.scheme) {
                                                "file" -> {
                                                    val file = File(uri.path ?: "")
                                                    if (file.exists()) {
                                                        // Offer to open the file
                                                        AlertDialog.Builder(context)
                                                            .setTitle("Download Complete")
                                                            .setMessage("Do you want to open this file?")
                                                            .setPositiveButton("Open") { _, _ ->
                                                                openDownloadedFile(file, mimeType ?: getMimeType(filename))
                                                            }
                                                            .setNegativeButton("Later", null)
                                                            .show()
                                                    }
                                                }
                                                "content" -> {
                                                    // For content URIs, use a different approach
                                                    val intent = Intent(Intent.ACTION_VIEW)
                                                    intent.setDataAndType(uri, getMimeType(uri.lastPathSegment ?: filename))
                                                    intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                                                    
                                                    if (intent.resolveActivity(packageManager) != null) {
                                                        AlertDialog.Builder(context)
                                                            .setTitle("Download Complete")
                                                            .setMessage("Do you want to open this file?")
                                                            .setPositiveButton("Open") { _, _ ->
                                                                startActivity(intent)
                                                            }
                                                            .setNegativeButton("Later", null)
                                                            .show()
                                                    }
                                                }
                                                else -> {
                                                    // Handle other URI schemes if needed
                                                    Toast.makeText(context, "Unknown file location", Toast.LENGTH_SHORT).show()
                                                }
                                            }
                                        } catch (e: Exception) {
                                            e.printStackTrace()
                                        }
                                    }
                                }
                            }
                            DownloadManager.STATUS_FAILED -> {
                                val reason = if (reasonColumnIndex >= 0) cursor.getInt(reasonColumnIndex) else -1
                                val errorMessage = getDownloadErrorMessage(reason)
                                Toast.makeText(context, "Download failed: $errorMessage", Toast.LENGTH_LONG).show()
                                
                                // Get the original URL and try enhanced fallback
                                val uriIndex = cursor.getColumnIndex(DownloadManager.COLUMN_URI)
                                if (uriIndex >= 0) {
                                    val originalUrl = cursor.getString(uriIndex)
                                    if (!originalUrl.isNullOrEmpty()) {
                                        AlertDialog.Builder(context)
                                            .setTitle("Download Failed")
                                            .setMessage("Would you like to try an alternative download method?")
                                            .setPositiveButton("Try Again") { _, _ ->
                                                directDownloadWithProgress(originalUrl, 
                                                    URLUtil.guessFileName(originalUrl, null, null),
                                                    "Mozilla/5.0")
                                            }
                                            .setNegativeButton("Cancel", null)
                                            .show()
                                    }
                                }
                            }
                        }
                    }
                }
                cursor.close()
            }
        }
    }
    
    private fun getDownloadErrorMessage(reason: Int): String {
        return when (reason) {
            DownloadManager.ERROR_CANNOT_RESUME -> "Cannot resume download"
            DownloadManager.ERROR_DEVICE_NOT_FOUND -> "Storage device not found"
            DownloadManager.ERROR_FILE_ALREADY_EXISTS -> "File already exists"
            DownloadManager.ERROR_FILE_ERROR -> "File error"
            DownloadManager.ERROR_HTTP_DATA_ERROR -> "HTTP data error"
            DownloadManager.ERROR_INSUFFICIENT_SPACE -> "Insufficient storage space"
            DownloadManager.ERROR_TOO_MANY_REDIRECTS -> "Too many redirects"
            DownloadManager.ERROR_UNHANDLED_HTTP_CODE -> "Unhandled HTTP code"
            DownloadManager.ERROR_UNKNOWN -> "Unknown error"
            else -> "Error code: $reason"
        }
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    override fun onDestroy() {
        // Clean up WebView and receivers
        try {
            unregisterReceiver(onDownloadComplete)
        } catch (e: Exception) {
            // Receiver may not be registered
        }
        
        webView.stopLoading()
        webView.clearHistory()
        webView.clearCache(true)
        webView.destroy()
        super.onDestroy()
    }
    
    // Add this method to detect if the device is running Android 11+ and has the needed permission
    private fun hasStoragePermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            Environment.isExternalStorageManager()
        } else {
            ContextCompat.checkSelfPermission(
                this,
                android.Manifest.permission.WRITE_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    private fun injectDownloadInterceptor() {
        val javascript = """
        javascript:(function() {
            // Helper function to check if a URL is a downloadable file
            function isDownloadableFile(url) {
                if (!url) return false;
                var lowercaseUrl = url.toLowerCase();
                return lowercaseUrl.endsWith('.pdf') || 
                       lowercaseUrl.endsWith('.doc') || 
                       lowercaseUrl.endsWith('.docx') ||
                       lowercaseUrl.endsWith('.xls') || 
                       lowercaseUrl.endsWith('.xlsx') || 
                       lowercaseUrl.endsWith('.ppt') ||
                       lowercaseUrl.endsWith('.pptx') ||
                       lowercaseUrl.endsWith('.zip') || 
                       lowercaseUrl.endsWith('.rar') ||
                       lowercaseUrl.includes('/download') || 
                       lowercaseUrl.includes('attachment') ||
                       lowercaseUrl.includes('dl=1');
            }
            
            // Intercept all link clicks
            document.addEventListener('click', function(e) {
                var target = e.target;
                while (target && target.tagName !== 'A') {
                    target = target.parentElement;
                }
                
                if (target && target.tagName === 'A') {
                    var url = target.href;
                    if (isDownloadableFile(url)) {
                        e.preventDefault();
                        e.stopPropagation();
                        window.Android.downloadFile(url);
                        return false;
                    }
                }
            }, true);
            
            // Look for download buttons and add event listeners
            var downloadLinks = document.querySelectorAll('a[download], a[href*="download"], .download-button, button[download]');
            for (var i = 0; i < downloadLinks.length; i++) {
                downloadLinks[i].addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    window.Android.downloadFile(this.href || this.getAttribute('data-href') || '');
                    return false;
                }, true);
            }
        })();
        """
        
        webView.evaluateJavascript(javascript, null)
    }

    private fun getMimeType(filename: String): String {
        return when {
            filename.endsWith(".pdf", true) -> "application/pdf"
            filename.endsWith(".doc", true) -> "application/msword"
            filename.endsWith(".docx", true) -> "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            filename.endsWith(".xls", true) -> "application/vnd.ms-excel"
            filename.endsWith(".xlsx", true) -> "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            filename.endsWith(".ppt", true) -> "application/vnd.ms-powerpoint"
            filename.endsWith(".pptx", true) -> "application/vnd.openxmlformats-officedocument.presentationml.presentation"
            filename.endsWith(".zip", true) -> "application/zip"
            filename.endsWith(".rar", true) -> "application/x-rar-compressed"
            filename.endsWith(".mp3", true) -> "audio/mpeg"
            filename.endsWith(".mp4", true) -> "video/mp4"
            filename.endsWith(".jpg", true) || filename.endsWith(".jpeg", true) -> "image/jpeg"
            filename.endsWith(".png", true) -> "image/png"
            filename.endsWith(".txt", true) -> "text/plain"
            filename.endsWith(".csv", true) -> "text/csv"
            filename.endsWith(".json", true) -> "application/json"
            filename.endsWith(".htm", true) || filename.endsWith(".html", true) -> "text/html"
            else -> {
                try {
                    val extension = MimeTypeMap.getFileExtensionFromUrl(filename)
                    MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension) ?: "application/octet-stream"
                } catch (e: Exception) {
                    "application/octet-stream"  // Default MIME type when unknown
                }
            }
        }
    }

    // Add this method to open downloaded files after completion
    private fun openDownloadedFile(file: File, mimeType: String) {
        try {
            val uri = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                FileProvider.getUriForFile(
                    this,
                    "${packageName}.fileprovider",
                    file
                )
            } else {
                Uri.fromFile(file)
            }
            
            val intent = Intent(Intent.ACTION_VIEW).apply {
                setDataAndType(uri, mimeType)
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            
            // Show open file dialog
            val chooser = Intent.createChooser(intent, "Open with")
            if (intent.resolveActivity(packageManager) != null) {
                startActivity(chooser)
            } else {
                Toast.makeText(this, "No app found to open this file type", Toast.LENGTH_LONG).show()
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Toast.makeText(this, "Cannot open file: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    // Add this method if it's missing from your code
    private fun tryFallbackDownload(url: String) {
        try {
            // Try to use the system download manager as a fallback
            val request = DownloadManager.Request(Uri.parse(url))
            val filename = URLUtil.guessFileName(url, null, null)
            
            // This is critical - allow downloads over mobile data
            request.setAllowedOverMetered(true)
            request.setAllowedOverRoaming(true)
            
            // Set download location
            request.setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, filename)
            
            // Set other properties
            request.setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED)
            request.setTitle(filename)
            request.setDescription("Downloading via fallback method")
            
            // Add request headers
            request.addRequestHeader("User-Agent", "Mozilla/5.0")
            request.addRequestHeader("Accept", "*/*")
            
            // Start download
            val dm = getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
            downloadID = dm.enqueue(request)
            
            Toast.makeText(this, "Trying fallback download...", Toast.LENGTH_SHORT).show()
            
        } catch (e: Exception) {
            // If all else fails, try to open the URL in an external browser
            try {
                val intent = Intent(Intent.ACTION_VIEW).apply {
                    data = Uri.parse(url)
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                startActivity(intent)
            } catch (e2: Exception) {
                Toast.makeText(this, "All download methods failed, please try a different link.", Toast.LENGTH_LONG).show()
            }
        }
    }
}