<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@drawable/bottom_sheet_background">

    <!-- Header -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Select File Type"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <!-- Select Image Option -->
    <LinearLayout
        android:id="@+id/selectImage"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="12dp"
        android:background="?android:attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_image"
            android:tint="@color/primary_color"
            android:layout_marginEnd="16dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Select Image"
            android:textSize="16sp"
            android:textColor="@color/text_primary" />

    </LinearLayout>

    <!-- Select Document Option -->
    <LinearLayout
        android:id="@+id/selectDocument"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="12dp"
        android:background="?android:attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_document"
            android:tint="@color/primary_color"
            android:layout_marginEnd="16dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Select Document"
            android:textSize="16sp"
            android:textColor="@color/text_primary" />

    </LinearLayout>

    <!-- Select Multiple Files Option -->
    <LinearLayout
        android:id="@+id/selectMultiple"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="12dp"
        android:background="?android:attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_multiple_files"
            android:tint="@color/primary_color"
            android:layout_marginEnd="16dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Select Multiple Files"
            android:textSize="16sp"
            android:textColor="@color/text_primary" />

    </LinearLayout>

    <!-- Take Photo Option -->
    <LinearLayout
        android:id="@+id/takePhoto"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="12dp"
        android:background="?android:attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_camera"
            android:tint="@color/primary_color"
            android:layout_marginEnd="16dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Take Photo"
            android:textSize="16sp"
            android:textColor="@color/text_primary" />

    </LinearLayout>

    <!-- Divider -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/divider_color"
        android:layout_marginVertical="8dp" />

    <!-- Cancel Button -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:text="Cancel"
        android:textSize="16sp"
        android:textColor="@color/text_secondary"
        android:gravity="center"
        android:background="?android:attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true" />

</LinearLayout>
